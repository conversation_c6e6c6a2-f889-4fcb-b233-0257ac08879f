import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')


class DataProcessor:
    """数据处理、特征构建、数据集划分类"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.feature_names = []
        
    def load_data(self):
        """加载和合并数据"""
        print("正在加载数据...")
        
        # 加载元素门捷列夫数数据
        elements_df = pd.read_excel('elements_data.xlsx')
        mendeleev_dict = dict(zip(elements_df['Symbol'], elements_df['Mendeleev_Number']))
        
        # 加载A位和B位元素数据
        a_df = pd.read_excel('A_site_elements_fully_expanded.xlsx')
        b_df = pd.read_excel('B_site_elements_fully_expanded.xlsx')
        
        # 合并数据（基于No.1列）
        merged_df = pd.merge(a_df, b_df, on=['No.1', 'DOI', 'component', 'E (kV cm-1)', 'Wrec (J cm-3)', 'η'])
        
        print(f"合并后数据形状: {merged_df.shape}")
        return merged_df, mendeleev_dict
    
    def calculate_mixing_entropy(self, elements, contents):
        """计算混合熵"""
        if not elements or not contents:
            return 0.0

        # 确保contents是数值类型且归一化
        valid_contents = []
        for content in contents:
            try:
                c = float(content)
                if c > 0:
                    valid_contents.append(c)
            except (ValueError, TypeError):
                continue

        if not valid_contents:
            return 0.0

        # 归一化
        total = sum(valid_contents)
        if total <= 0:
            return 0.0

        fractions = [c / total for c in valid_contents]

        # 计算混合熵: S_mix = -R * Σ(x_i * ln(x_i))
        # 这里使用简化版本，不包含气体常数R
        entropy = 0.0
        for fraction in fractions:
            if fraction > 0:
                entropy -= fraction * np.log(fraction)

        return entropy

    def extract_features(self, df, mendeleev_dict):
        """提取特征"""
        print("正在提取特征...")

        features = []

        for idx, row in df.iterrows():
            feature_row = {}

            # 特征1: 测试电场
            feature_row['electric_field'] = row['E (kV cm-1)']

            # A位元素特征
            a_elements = []
            a_contents = []
            for i in range(1, 8):  # A_element_1 到 A_element_7
                element_col = f'A_element_{i}'
                content_col = f'A_content_{i}'
                if element_col in row and pd.notna(row[element_col]):
                    element = str(row[element_col]).strip()
                    # 清理元素符号（去除化学价）
                    clean_element = element.split('⁺')[0].split('²')[0].split('³')[0].split('⁴')[0].split('⁵')[0]
                    if clean_element in mendeleev_dict:
                        a_elements.append(mendeleev_dict[clean_element])
                        content = row[content_col] if pd.notna(row[content_col]) else 0
                        # 确保content是数值类型
                        try:
                            content = float(content)
                        except (ValueError, TypeError):
                            content = 0.0
                        a_contents.append(content)

            # B位元素特征
            b_elements = []
            b_contents = []
            for i in range(1, 6):  # B_element_1 到 B_element_5
                element_col = f'B_element_{i}'
                content_col = f'B_content_{i}'
                if element_col in row and pd.notna(row[element_col]):
                    element = str(row[element_col]).strip()
                    # 清理元素符号（去除化学价）
                    clean_element = element.split('⁺')[0].split('²')[0].split('³')[0].split('⁴')[0].split('⁵')[0]
                    if clean_element in mendeleev_dict:
                        b_elements.append(mendeleev_dict[clean_element])
                        content = row[content_col] if pd.notna(row[content_col]) else 0
                        # 确保content是数值类型
                        try:
                            content = float(content)
                        except (ValueError, TypeError):
                            content = 0.0
                        b_contents.append(content)

            # 特征2: A位加权平均门捷列夫数
            if a_elements and a_contents:
                total_a_content = sum(a_contents)
                if total_a_content > 0:
                    feature_row['A_weighted_mendeleev'] = sum(m*c for m, c in zip(a_elements, a_contents)) / total_a_content
                else:
                    feature_row['A_weighted_mendeleev'] = np.mean(a_elements) if a_elements else 0
            else:
                feature_row['A_weighted_mendeleev'] = 0

            # 特征3: B位加权平均门捷列夫数
            if b_elements and b_contents:
                total_b_content = sum(b_contents)
                if total_b_content > 0:
                    feature_row['B_weighted_mendeleev'] = sum(m*c for m, c in zip(b_elements, b_contents)) / total_b_content
                else:
                    feature_row['B_weighted_mendeleev'] = np.mean(b_elements) if b_elements else 0
            else:
                feature_row['B_weighted_mendeleev'] = 0

            # 特征4: A位混合熵
            feature_row['A_mixing_entropy'] = self.calculate_mixing_entropy(a_elements, a_contents)

            # 特征5: B位混合熵
            feature_row['B_mixing_entropy'] = self.calculate_mixing_entropy(b_elements, b_contents)

            # 目标变量
            feature_row['energy_density'] = row['Wrec (J cm-3)']
            feature_row['energy_efficiency'] = row['η']

            # 用于分组的标识符（同一文献同一化学成分）
            feature_row['group_id'] = f"{row['DOI']}_{row['component']}"

            features.append(feature_row)

        feature_df = pd.DataFrame(features)

        # 移除包含NaN的行
        feature_df = feature_df.dropna()

        print(f"提取特征后数据形状: {feature_df.shape}")
        print(f"使用的特征: 测试电场, A位门捷列夫数, B位门捷列夫数, A位混合熵, B位混合熵")
        return feature_df
    
    def split_data_by_group(self, df, test_size=0.2, val_size=0.1, include_electric_field_for_efficiency=False):
        """按组分割数据，避免数据泄露"""
        print("正在按组分割数据...")

        # 准备特征和目标变量
        # 储能密度预测使用所有特征（包括电场）
        density_feature_cols = ['electric_field', 'A_weighted_mendeleev', 'B_weighted_mendeleev', 'A_mixing_entropy', 'B_mixing_entropy']

        # 储能效率预测不使用电场特征
        if include_electric_field_for_efficiency:
            efficiency_feature_cols = ['electric_field', 'A_weighted_mendeleev', 'B_weighted_mendeleev', 'A_mixing_entropy', 'B_mixing_entropy']
        else:
            efficiency_feature_cols = ['A_weighted_mendeleev', 'B_weighted_mendeleev', 'A_mixing_entropy', 'B_mixing_entropy']

        X_density = df[density_feature_cols]
        X_efficiency = df[efficiency_feature_cols]
        y_density = df['energy_density']
        y_efficiency = df['energy_efficiency']
        groups = df['group_id']

        self.density_feature_names = density_feature_cols
        self.efficiency_feature_names = efficiency_feature_cols

        # 获取所有唯一的组
        unique_groups = groups.unique()
        np.random.seed(42)
        shuffled_groups = np.random.permutation(unique_groups)

        # 计算分割点
        n_groups = len(unique_groups)
        test_groups_count = int(n_groups * test_size)
        val_groups_count = int(n_groups * val_size)

        # 分割组
        test_groups = set(shuffled_groups[:test_groups_count])
        val_groups = set(shuffled_groups[test_groups_count:test_groups_count + val_groups_count])
        train_groups = set(shuffled_groups[test_groups_count + val_groups_count:])

        # 根据组分割数据
        train_mask = groups.isin(train_groups)
        val_mask = groups.isin(val_groups)
        test_mask = groups.isin(test_groups)

        # 储能密度数据分割
        X_density_train = X_density[train_mask]
        X_density_val = X_density[val_mask]
        X_density_test = X_density[test_mask]

        # 储能效率数据分割
        X_efficiency_train = X_efficiency[train_mask]
        X_efficiency_val = X_efficiency[val_mask]
        X_efficiency_test = X_efficiency[test_mask]

        y_density_train = y_density[train_mask]
        y_density_val = y_density[val_mask]
        y_density_test = y_density[test_mask]

        y_efficiency_train = y_efficiency[train_mask]
        y_efficiency_val = y_efficiency[val_mask]
        y_efficiency_test = y_efficiency[test_mask]

        print(f"训练集大小: {X_density_train.shape[0]}")
        print(f"验证集大小: {X_density_val.shape[0]}")
        print(f"测试集大小: {X_density_test.shape[0]}")

        print(f"训练集组数: {len(train_groups)}")
        print(f"验证集组数: {len(val_groups)}")
        print(f"测试集组数: {len(test_groups)}")
        print(f"训练-验证组重叠: {len(train_groups & val_groups)}")
        print(f"训练-测试组重叠: {len(train_groups & test_groups)}")
        print(f"验证-测试组重叠: {len(val_groups & test_groups)}")

        print(f"储能密度特征: {self.density_feature_names}")
        print(f"储能效率特征: {self.efficiency_feature_names}")

        # 验证分割的正确性
        assert len(train_groups & val_groups) == 0, "训练集和验证集有组重叠！"
        assert len(train_groups & test_groups) == 0, "训练集和测试集有组重叠！"
        assert len(val_groups & test_groups) == 0, "验证集和测试集有组重叠！"

        return {
            'density': (X_density_train, X_density_val, X_density_test,
                       y_density_train, y_density_val, y_density_test),
            'efficiency': (X_efficiency_train, X_efficiency_val, X_efficiency_test,
                          y_efficiency_train, y_efficiency_val, y_efficiency_test)
        }

    def prepare_data(self):
        """完整的数据准备流程"""
        # 加载数据
        df, mendeleev_dict = self.load_data()
        
        # 提取特征
        feature_df = self.extract_features(df, mendeleev_dict)
        
        # 分割数据
        data_splits = self.split_data_by_group(feature_df)
        
        return feature_df, data_splits
