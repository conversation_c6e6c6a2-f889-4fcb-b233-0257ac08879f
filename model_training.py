import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from data_processing import DataProcessor
from model_selection import ModelSelector
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


class ModelTrainer:
    """模型训练和可视化类"""
    
    def __init__(self, use_hyperparameter_tuning=False):
        self.data_processor = DataProcessor()
        self.model_selector = ModelSelector()
        self.use_hyperparameter_tuning = use_hyperparameter_tuning
        self.results = {}
        
    def plot_results(self, y_true, y_pred, title, target_name, model, feature_names):
        """绘制预测结果"""
        plt.figure(figsize=(12, 10))
        
        # 设置中文标签映射
        if 'density' in target_name.lower() or '密度' in target_name:
            target_label = 'Energy Density (J/cm³)'
            target_cn = '储能密度'
        else:
            target_label = 'Energy Efficiency (%)'
            target_cn = '储能效率'
        
        # 预测vs真实值散点图
        plt.subplot(2, 2, 1)
        plt.scatter(y_true, y_pred, alpha=0.6, s=20)
        plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        plt.xlabel(f'True {target_label}', fontsize=12)
        plt.ylabel(f'Predicted {target_label}', fontsize=12)
        plt.title(f'{target_cn}预测 - Prediction vs True Values', fontsize=14)
        plt.grid(True, alpha=0.3)
        
        # 残差图
        plt.subplot(2, 2, 2)
        residuals = y_true - y_pred
        plt.scatter(y_pred, residuals, alpha=0.6, s=20)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel(f'Predicted {target_label}', fontsize=12)
        plt.ylabel('Residuals', fontsize=12)
        plt.title(f'{target_cn}预测 - Residual Plot', fontsize=14)
        plt.grid(True, alpha=0.3)
        
        # 残差直方图
        plt.subplot(2, 2, 3)
        plt.hist(residuals, bins=30, alpha=0.7, edgecolor='black')
        plt.xlabel('Residuals', fontsize=12)
        plt.ylabel('Frequency', fontsize=12)
        plt.title(f'{target_cn}预测 - Residual Distribution', fontsize=14)
        plt.grid(True, alpha=0.3)
        
        # 特征重要性
        plt.subplot(2, 2, 4)
        feature_importance = self.model_selector.get_feature_importance(model, feature_names)
        
        if feature_importance:
            # 特征名称映射
            feature_name_map = {
                'electric_field': 'Electric Field',
                'A_weighted_mendeleev': 'A-site Mendeleev',
                'B_weighted_mendeleev': 'B-site Mendeleev',
                'A_mixing_entropy': 'A-site Entropy',
                'B_mixing_entropy': 'B-site Entropy'
            }
            
            # 排序特征重要性
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            feature_labels = [feature_name_map.get(name, name) for name, _ in sorted_features]
            importances = [importance for _, importance in sorted_features]
            
            plt.bar(range(len(importances)), importances)
            plt.xticks(range(len(importances)), feature_labels, rotation=45, ha='right')
            plt.ylabel('Importance', fontsize=12)
            plt.title(f'{target_cn}预测 - Feature Importance', fontsize=14)
            plt.grid(True, alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'Feature Importance\nNot Available\nfor This Model',
                    ha='center', va='center', transform=plt.gca().transAxes, fontsize=12)
            plt.title(f'{target_cn}预测 - Feature Importance', fontsize=14)
        
        plt.tight_layout()
        
        # 保存文件名使用英文
        safe_filename = target_cn.replace('储能密度', 'energy_density').replace('储能效率', 'energy_efficiency')
        plt.savefig(f'{safe_filename}_prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_model_comparison(self, density_cv_results, efficiency_cv_results):
        """绘制模型比较图"""
        plt.figure(figsize=(15, 6))
        
        # 储能密度模型比较
        plt.subplot(1, 2, 1)
        models = list(density_cv_results.keys())
        means = [density_cv_results[model]['mean_r2'] for model in models]
        stds = [density_cv_results[model]['std_r2'] for model in models]
        
        bars = plt.bar(models, means, yerr=stds, capsize=5, alpha=0.7)
        plt.ylabel('Cross-Validation R²', fontsize=12)
        plt.title('储能密度预测 - 模型性能比较', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, mean, std in zip(bars, means, stds):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                    f'{mean:.3f}', ha='center', va='bottom', fontsize=10)
        
        # 储能效率模型比较
        plt.subplot(1, 2, 2)
        models = list(efficiency_cv_results.keys())
        means = [efficiency_cv_results[model]['mean_r2'] for model in models]
        stds = [efficiency_cv_results[model]['std_r2'] for model in models]
        
        bars = plt.bar(models, means, yerr=stds, capsize=5, alpha=0.7)
        plt.ylabel('Cross-Validation R²', fontsize=12)
        plt.title('储能效率预测 - 模型性能比较', fontsize=14)
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, mean, std in zip(bars, means, stds):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                    f'{mean:.3f}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def train_and_evaluate(self):
        """完整的训练和评估流程"""
        print("开始储能材料预测模型训练...")
        
        # 数据准备
        feature_df, data_splits = self.data_processor.prepare_data()
        
        (X_train, X_val, X_test,
         y_density_train, y_density_val, y_density_test,
         y_efficiency_train, y_efficiency_val, y_efficiency_test) = data_splits
        
        # 合并训练和验证数据用于交叉验证
        X_train_val = pd.concat([X_train, X_val])
        y_density_train_val = pd.concat([y_density_train, y_density_val])
        y_efficiency_train_val = pd.concat([y_efficiency_train, y_efficiency_val])
        
        # 获取对应的组信息
        train_val_indices = list(X_train.index) + list(X_val.index)
        groups_train_val = feature_df.loc[train_val_indices, 'group_id']
        
        # 预测储能密度
        print("\n" + "="*60)
        print("储能密度预测")
        print("="*60)
        density_cv_results, best_density_model, best_density_model_obj = self.model_selector.train_models_with_cv(
            X_train_val, y_density_train_val, groups_train_val, "储能密度", self.use_hyperparameter_tuning
        )
        
        # 评估储能密度模型
        use_scaling_density = best_density_model == 'Linear Regression'
        density_test_results = self.model_selector.evaluate_model(
            best_density_model_obj, X_test, y_density_test,
            best_density_model, "储能密度", use_scaling_density
        )
        
        # 预测储能效率
        print("\n" + "="*60)
        print("储能效率预测")
        print("="*60)
        efficiency_cv_results, best_efficiency_model, best_efficiency_model_obj = self.model_selector.train_models_with_cv(
            X_train_val, y_efficiency_train_val, groups_train_val, "储能效率", self.use_hyperparameter_tuning
        )
        
        # 评估储能效率模型
        use_scaling_efficiency = best_efficiency_model == 'Linear Regression'
        efficiency_test_results = self.model_selector.evaluate_model(
            best_efficiency_model_obj, X_test, y_efficiency_test,
            best_efficiency_model, "储能效率", use_scaling_efficiency
        )
        
        # 绘制结果
        self.plot_results(y_density_test, density_test_results['predictions'],
                         f"储能密度预测 ({best_density_model})", "储能密度", 
                         best_density_model_obj, self.data_processor.feature_names)
        
        self.plot_results(y_efficiency_test, efficiency_test_results['predictions'],
                         f"储能效率预测 ({best_efficiency_model})", "储能效率",
                         best_efficiency_model_obj, self.data_processor.feature_names)
        
        # 绘制模型比较图
        self.plot_model_comparison(density_cv_results, efficiency_cv_results)
        
        # 保存结果
        self.results = {
            'density_results': density_test_results,
            'efficiency_results': efficiency_test_results,
            'best_density_model': best_density_model,
            'best_efficiency_model': best_efficiency_model,
            'density_cv_results': density_cv_results,
            'efficiency_cv_results': efficiency_cv_results,
            'best_density_model_obj': best_density_model_obj,
            'best_efficiency_model_obj': best_efficiency_model_obj
        }
        
        return self.results
    
    def print_summary(self):
        """打印结果总结"""
        if not self.results:
            print("请先运行 train_and_evaluate() 方法")
            return
        
        print("\n" + "="*60)
        print("最终结果总结")
        print("="*60)
        print(f"储能密度预测最佳模型: {self.results['best_density_model']}")
        print(f"  测试集R²: {self.results['density_results']['r2']:.4f}")
        print(f"  测试集RMSE: {self.results['density_results']['rmse']:.4f}")
        print(f"  测试集MAE: {self.results['density_results']['mae']:.4f}")
        
        print(f"\n储能效率预测最佳模型: {self.results['best_efficiency_model']}")
        print(f"  测试集R²: {self.results['efficiency_results']['r2']:.4f}")
        print(f"  测试集RMSE: {self.results['efficiency_results']['rmse']:.4f}")
        print(f"  测试集MAE: {self.results['efficiency_results']['mae']:.4f}")


if __name__ == "__main__":
    # 创建训练器实例
    trainer = ModelTrainer(use_hyperparameter_tuning=False)  # 设置为True启用超参数调优
    
    # 训练和评估模型
    results = trainer.train_and_evaluate()
    
    # 打印总结
    trainer.print_summary()
