"""
储能材料预测主程序
重构后的模块化版本

使用方法:
1. 基础训练: python main.py
2. 启用超参数调优: python main.py --tune
3. 只进行数据处理: python main.py --data-only
4. 只进行模型比较: python main.py --compare-only
"""

import argparse
import sys
from data_processing import DataProcessor
from model_selection import ModelSelector
from model_training import ModelTrainer


def main():
    parser = argparse.ArgumentParser(description='储能材料预测模型训练')
    parser.add_argument('--tune', action='store_true', 
                       help='启用超参数调优 (会增加训练时间)')
    parser.add_argument('--data-only', action='store_true',
                       help='只进行数据处理和特征提取')
    parser.add_argument('--compare-only', action='store_true',
                       help='只进行模型比较，不进行超参数调优')
    
    args = parser.parse_args()
    
    try:
        if args.data_only:
            # 只进行数据处理
            print("=" * 60)
            print("数据处理模式")
            print("=" * 60)
            
            processor = DataProcessor()
            feature_df, data_splits = processor.prepare_data()
            
            print(f"\n数据处理完成!")
            print(f"特征数据形状: {feature_df.shape}")
            print(f"特征列: {processor.feature_names}")
            
            # 保存处理后的数据
            feature_df.to_csv('processed_features.csv', index=False)
            print("处理后的特征数据已保存到 'processed_features.csv'")
            
        elif args.compare_only:
            # 只进行模型比较
            print("=" * 60)
            print("模型比较模式")
            print("=" * 60)
            
            trainer = ModelTrainer(use_hyperparameter_tuning=False)
            results = trainer.train_and_evaluate()
            trainer.print_summary()
            
        else:
            # 完整训练流程
            print("=" * 60)
            print("完整训练模式")
            print("=" * 60)
            
            if args.tune:
                print("启用超参数调优模式 (这可能需要较长时间)")
                trainer = ModelTrainer(use_hyperparameter_tuning=True)
            else:
                print("使用默认参数模式")
                trainer = ModelTrainer(use_hyperparameter_tuning=False)
            
            # 训练和评估
            results = trainer.train_and_evaluate()
            trainer.print_summary()
            
            # 显示特征重要性
            print("\n" + "=" * 60)
            print("特征重要性分析")
            print("=" * 60)
            
            # 储能密度模型特征重要性
            density_importance = trainer.model_selector.get_feature_importance(
                results['best_density_model_obj'], 
                trainer.data_processor.feature_names
            )
            
            if density_importance:
                print(f"\n储能密度预测模型 ({results['best_density_model']}) 特征重要性:")
                for feature, importance in sorted(density_importance.items(), 
                                                key=lambda x: x[1], reverse=True):
                    print(f"  {feature}: {importance:.4f}")
            
            # 储能效率模型特征重要性
            efficiency_importance = trainer.model_selector.get_feature_importance(
                results['best_efficiency_model_obj'], 
                trainer.data_processor.feature_names
            )
            
            if efficiency_importance:
                print(f"\n储能效率预测模型 ({results['best_efficiency_model']}) 特征重要性:")
                for feature, importance in sorted(efficiency_importance.items(), 
                                                key=lambda x: x[1], reverse=True):
                    print(f"  {feature}: {importance:.4f}")
    
    except FileNotFoundError as e:
        print(f"错误: 找不到数据文件 - {e}")
        print("请确保以下文件存在于当前目录:")
        print("  - elements_data.xlsx")
        print("  - A_site_elements_fully_expanded.xlsx") 
        print("  - B_site_elements_fully_expanded.xlsx")
        sys.exit(1)
        
    except Exception as e:
        print(f"运行时错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def demo_individual_modules():
    """演示如何单独使用各个模块"""
    print("=" * 60)
    print("模块使用演示")
    print("=" * 60)
    
    # 1. 数据处理模块
    print("\n1. 数据处理模块演示:")
    processor = DataProcessor()
    feature_df, data_splits = processor.prepare_data()
    
    # 2. 模型选择模块
    print("\n2. 模型选择模块演示:")
    selector = ModelSelector()
    
    # 解包数据
    (X_train, X_val, X_test,
     y_density_train, y_density_val, y_density_test,
     y_efficiency_train, y_efficiency_val, y_efficiency_test) = data_splits
    
    # 合并训练和验证数据
    import pandas as pd
    X_train_val = pd.concat([X_train, X_val])
    y_density_train_val = pd.concat([y_density_train, y_density_val])
    
    # 获取组信息
    train_val_indices = list(X_train.index) + list(X_val.index)
    groups_train_val = feature_df.loc[train_val_indices, 'group_id']
    
    # 训练模型
    cv_results, best_model_name, best_model = selector.train_models_with_cv(
        X_train_val, y_density_train_val, groups_train_val, "储能密度"
    )
    
    # 评估模型
    test_results = selector.evaluate_model(
        best_model, X_test, y_density_test, best_model_name, "储能密度"
    )
    
    print(f"最佳模型: {best_model_name}")
    print(f"测试集R²: {test_results['r2']:.4f}")


if __name__ == "__main__":
    main()
