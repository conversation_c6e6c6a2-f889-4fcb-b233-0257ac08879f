"""
测试重构后的模块
用于验证各个模块是否正常工作
"""

import os
import sys
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

def test_data_processing():
    """测试数据处理模块"""
    print("测试数据处理模块...")
    
    try:
        from data_processing import DataProcessor
        
        processor = DataProcessor()
        
        # 测试混合熵计算
        entropy = processor.calculate_mixing_entropy([1, 2, 3], [0.5, 0.3, 0.2])
        print(f"  混合熵计算测试: {entropy:.4f}")
        
        # 测试空输入
        entropy_empty = processor.calculate_mixing_entropy([], [])
        print(f"  空输入混合熵: {entropy_empty}")
        
        print("  数据处理模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"  数据处理模块测试失败: {e}")
        return False

def test_model_selection():
    """测试模型选择模块"""
    print("测试模型选择模块...")
    
    try:
        from model_selection import ModelSelector
        
        selector = ModelSelector()
        
        # 测试模型初始化
        print(f"  可用模型: {list(selector.models.keys())}")
        print(f"  参数网格: {list(selector.param_grids.keys())}")
        
        # 创建模拟数据测试特征重要性
        from sklearn.ensemble import RandomForestRegressor
        model = RandomForestRegressor(n_estimators=10, random_state=42)
        X_mock = np.random.rand(100, 5)
        y_mock = np.random.rand(100)
        model.fit(X_mock, y_mock)
        
        feature_names = ['f1', 'f2', 'f3', 'f4', 'f5']
        importance = selector.get_feature_importance(model, feature_names)
        print(f"  特征重要性测试: {len(importance)} 个特征")
        
        print("  模型选择模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"  模型选择模块测试失败: {e}")
        return False

def test_model_training():
    """测试模型训练模块"""
    print("测试模型训练模块...")
    
    try:
        from model_training import ModelTrainer
        
        # 测试初始化
        trainer = ModelTrainer(use_hyperparameter_tuning=False)
        print("  模型训练器初始化成功")
        
        # 测试matplotlib配置
        import matplotlib.pyplot as plt
        print(f"  中文字体配置: {plt.rcParams['font.sans-serif']}")
        
        print("  模型训练模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"  模型训练模块测试失败: {e}")
        return False

def test_imports():
    """测试所有模块的导入"""
    print("测试模块导入...")
    
    modules_to_test = [
        'data_processing',
        'model_selection', 
        'model_training'
    ]
    
    success_count = 0
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"  {module_name} 导入成功 ✓")
            success_count += 1
        except Exception as e:
            print(f"  {module_name} 导入失败: {e}")
    
    print(f"  导入测试完成: {success_count}/{len(modules_to_test)} 个模块成功")
    return success_count == len(modules_to_test)

def test_data_files_exist():
    """检查数据文件是否存在"""
    print("检查数据文件...")
    
    required_files = [
        'elements_data.xlsx',
        'A_site_elements_fully_expanded.xlsx',
        'B_site_elements_fully_expanded.xlsx'
    ]
    
    existing_files = []
    missing_files = []
    
    for file_name in required_files:
        if os.path.exists(file_name):
            existing_files.append(file_name)
            print(f"  {file_name} 存在 ✓")
        else:
            missing_files.append(file_name)
            print(f"  {file_name} 缺失 ✗")
    
    if missing_files:
        print(f"  警告: {len(missing_files)} 个数据文件缺失")
        print("  这些文件是运行完整程序所必需的")
    else:
        print("  所有数据文件都存在 ✓")
    
    return len(missing_files) == 0

def create_mock_data():
    """创建模拟数据用于测试"""
    print("创建模拟数据...")
    
    try:
        # 创建模拟的元素数据
        elements_data = pd.DataFrame({
            'Symbol': ['Ba', 'Sr', 'Ca', 'Ti', 'Zr', 'Hf'],
            'Mendeleev_Number': [10, 12, 14, 22, 40, 72]
        })
        
        # 创建模拟的A位数据
        a_site_data = pd.DataFrame({
            'No.1': [1, 2, 3],
            'DOI': ['doi1', 'doi2', 'doi3'],
            'component': ['comp1', 'comp2', 'comp3'],
            'E (kV cm-1)': [100, 150, 200],
            'Wrec (J cm-3)': [1.5, 2.0, 2.5],
            'η': [0.85, 0.90, 0.88],
            'A_element_1': ['Ba', 'Sr', 'Ca'],
            'A_content_1': [0.8, 0.7, 0.9],
            'A_element_2': ['Sr', 'Ca', 'Ba'],
            'A_content_2': [0.2, 0.3, 0.1]
        })
        
        # 创建模拟的B位数据
        b_site_data = pd.DataFrame({
            'No.1': [1, 2, 3],
            'DOI': ['doi1', 'doi2', 'doi3'],
            'component': ['comp1', 'comp2', 'comp3'],
            'E (kV cm-1)': [100, 150, 200],
            'Wrec (J cm-3)': [1.5, 2.0, 2.5],
            'η': [0.85, 0.90, 0.88],
            'B_element_1': ['Ti', 'Zr', 'Hf'],
            'B_content_1': [0.9, 0.8, 0.7],
            'B_element_2': ['Zr', 'Hf', 'Ti'],
            'B_content_2': [0.1, 0.2, 0.3]
        })
        
        # 保存模拟数据
        elements_data.to_excel('test_elements_data.xlsx', index=False)
        a_site_data.to_excel('test_A_site_elements_fully_expanded.xlsx', index=False)
        b_site_data.to_excel('test_B_site_elements_fully_expanded.xlsx', index=False)
        
        print("  模拟数据创建成功 ✓")
        return True
        
    except Exception as e:
        print(f"  模拟数据创建失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_elements_data.xlsx',
        'test_A_site_elements_fully_expanded.xlsx',
        'test_B_site_elements_fully_expanded.xlsx'
    ]
    
    for file_name in test_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"  删除测试文件: {file_name}")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("重构模块测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("数据文件检查", test_data_files_exist),
        ("数据处理模块", test_data_processing),
        ("模型选择模块", test_model_selection),
        ("模型训练模块", test_model_training),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{test_name}: {status} {symbol}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("所有测试通过! 重构成功 🎉")
    else:
        print("部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
